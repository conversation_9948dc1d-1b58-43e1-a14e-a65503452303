This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

# AI Tools Directory

A comprehensive directory of AI tools with advanced features including FAQ management, content generation, and admin tools.

## Features

### FAQ System
- **Database-Stored FAQs**: Persistent FAQ storage in Supabase with full CRUD operations
- **Backward Compatibility**: Seamless fallback to generated FAQs when database FAQs are unavailable
- **Admin Management**: Complete admin interface for FAQ creation, editing, and management
- **Categorization**: Support for FAQ categories (general, pricing, features, support, getting-started)
- **Priority & Ordering**: Configurable display order and priority levels
- **Source Tracking**: Track FAQ sources (manual, AI-generated, scraped, user-submitted)
- **Full-Text Search**: Advanced search capabilities with PostgreSQL full-text search
- **Performance Optimized**: Proper indexing and caching for optimal performance

### API Endpoints
- `GET /api/tools/[id]/faqs` - Public endpoint for tool FAQs
- `GET /api/admin/faqs` - Admin endpoint for all FAQs with filtering
- `POST /api/admin/faqs` - Create new FAQ (admin only)
- `PUT /api/admin/faqs/[id]` - Update existing FAQ (admin only)
- `DELETE /api/admin/faqs/[id]` - Delete FAQ (admin only)

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## Database Setup

### FAQ System Migration

To set up the FAQ system database tables, run the migration script:

```sql
-- Execute the FAQ system migration
\i src/lib/database/migrations/002_faq_system_schema.sql
```

This will create:
- `faqs` table with proper relationships and constraints
- Indexes for optimal performance
- Triggers for automatic timestamp updates
- Sample data for testing (optional)

### Environment Variables

Ensure you have the following environment variables set:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
ADMIN_API_KEY=your_admin_api_key
```

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Features

### FAQ System (Simplified JSONB)

The FAQ system provides comprehensive storage and management of frequently asked questions using a simplified JSONB-based approach:

- **JSONB Storage**: FAQs stored directly in tools table as JSONB column
- **Admin Integration**: FAQ management integrated into Add/Edit tool forms
- **Public API**: Read-only access to tool FAQs via `/api/tools/[id]/faqs`
- **Component Integration**: ToolQASection component with database and fallback support
- **Simplified Architecture**: No separate table, reduced complexity, better performance

#### Setup FAQ System

1. Apply the database migration:
```bash
# Add faqs JSONB column to tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS faqs JSONB;
CREATE INDEX IF NOT EXISTS idx_tools_faqs_gin ON tools USING gin(faqs);
```

2. Test the FAQ system:
```bash
npx tsx scripts/test-faq-system.ts
```

3. Use in admin forms:
- FAQs are integrated into Add Tool and Edit Tool forms
- Manage FAQs directly within the tool editing workflow
- Support for categories, priorities, and featured FAQs

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
